import { createUUID } from "../../../utils/utils";
import GlobalStore from "../../../core/globalStore/globalStore";
import { ContextMenu as ContextMenuXeokit } from "@xeokit/xeokit-sdk";
import { SectionBox } from "../sections/sectionBox";


export class ContextMenu {
    private _generalId = createUUID();
    private _store = GlobalStore.getInstance().getAll();
    private _canvas: HTMLCanvasElement | null = null;
    private _containerView: HTMLElement | null = null;
    contextMenu: HTMLElement | null = null;
    private _isRightClick = false;
    private _startX = 0;
    private _startY = 0;
    private _onMouseDown: ((event: MouseEvent) => void) | null = null;
    private _onMouseUp: ((event: MouseEvent) => void) | null = null;
    private _sectionBox = new SectionBox();

    private canvasContextMenu = new ContextMenuXeokit({
        enabled: true,
        context: {
            viewer: this._store.viewer,
        },
        items: [
            [
                {
                    title: "Isolate",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },

                    doAction: function (context: any) {
                        const scene = context.viewer.scene;
                        scene.setObjectsVisible(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.selectedObjectIds, false);
                        scene.setObjectsHighlighted(scene.selectedObjectIds, false);
                        scene.setObjectsSelected(scene.selectedObjectIds, false);
                        //Reset selections
                    }
                },
            ],
            [

                {
                    title: "Hide",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },
                    doAction: function (context: any) {
                        const viewer = context.viewer;
                        const scene = viewer.scene;

                        scene.setObjectsVisible(scene.selectedObjectIds, false);
                        // scene.setObjectsXRayed(scene.xrayedObjectIds, false);
                        // scene.setObjectsXRayed(scene.selectedObjectIds, true);
                        scene.setObjectsSelected(scene.selectedObjectIds, false);
                        scene.setObjectsHighlighted(scene.selectedObjectIds, false);


                    }
                },

                {
                    title: "Show All",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;

                        const checkVisible = scene.numVisibleObjects < scene.numObjects
                        const checkXRayed = scene.numXRayedObjects > 0;
                        if (checkVisible || checkXRayed) {
                            return true;
                        }
                        return false;
                    },
                    doAction: function (context: any) {
                        const scene = context.viewer.scene;
                        scene.setObjectsVisible(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.xrayedObjectIds, false);
                        scene.setObjectsSelected(scene.selectedObjectIds, false);
                    }
                },
                {
                    title: "test",

                    doAction: function (context: any) {
                        const scene = context.viewer.scene;
                        console.log("scene", scene);

                    }
                }
            ],
            [
                {
                    title: "View Fit All",
                    doAction: function (context: any) {
                        context.viewer.cameraFlight.flyTo({
                            aabb: context.viewer.scene.getAABB()
                        });
                    }
                }
            ],
            [
                {
                    title: "Section Box",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },
                    doAction: (context: any) => {
                        const getObjectIds = context.viewer.scene.selectedObjectIds;
                        const aabb = context.viewer.scene.getAABB(getObjectIds);
                        console.log("[ContextMenu] Section Box AABB:", aabb);
                        // const aabb = context.viewer.scene.getAABB();
                        this._sectionBox.setLimitDragging(false);
                        this._sectionBox.active = true
                        this._sectionBox.visible = true;
                        this._sectionBox.aabb = aabb;
                    }
                }
            ]
        ]
    });

    constructor() {
        this._canvas = this._store.canvas || null;
        this._containerView = this._store.containerViewer as HTMLElement || null;
        if (!this._canvas || !this._containerView) {
            throw new Error("Canvas not found in GlobalStore.");
        }
        this.initialize();
        this.saveOriginalMaterials();
        this.changeMaterial();

        GlobalStore.getInstance().set("contextMenu", this);
        const liElement = (this.canvasContextMenu as any)._rootMenu.menuElement
        this._store.containerCanvas?.appendChild(liElement);
    }

    //#region initialize
    public initialize() {
        this._onMouseDown = (event: MouseEvent) => {
            if (event.button === 2) {
                event.preventDefault();
                this._isRightClick = true;
                this._startX = event.clientX;
                this._startY = event.clientY;
            }
        };

        this._onMouseUp = (event: MouseEvent) => {
            if (this._isRightClick && event.button === 2) {
                const deltaX = Math.abs(event.clientX - this._startX);
                const deltaY = Math.abs(event.clientY - this._startY);

                if (deltaX < 5 && deltaY < 5) {
                    this.handleContextMenu(event);
                }
                this._isRightClick = false;
            }
        };

        this._canvas?.addEventListener('mousedown', this._onMouseDown);
        this._canvas?.addEventListener('mouseup', this._onMouseUp);


    }
    //#region handleContextMenu
    public handleContextMenu = (event: MouseEvent) => {
        // const canvasPos = this.getCanvasPosFromEvent(event);

        this.canvasContextMenu.context = { // Must set context before showing menu
            viewer: this._store.viewer,

        };
        this.canvasContextMenu.show(event.pageX, event.pageY);

        event.preventDefault();
    }



    private _originalMaterials: any = null;
    //#region saveOriginalMaterials
    public saveOriginalMaterials = () => {
        const viewer = this._store.viewer!;
        this._originalMaterials = {
            xrayMaterial: {
                fill: viewer.scene.xrayMaterial.fill,
                fillAlpha: viewer.scene.xrayMaterial.fillAlpha,
                fillColor: [...viewer.scene.xrayMaterial.fillColor],
                edgeAlpha: viewer.scene.xrayMaterial.edgeAlpha,
                edgeColor: [...viewer.scene.xrayMaterial.edgeColor],
            },

        };
    }
    //#region resetMaterials
    public resetMaterials = () => {
        if (!this._originalMaterials) return;
        const viewer = this._store.viewer!;
        const orig = this._originalMaterials;

        Object.assign(viewer.scene.xrayMaterial, orig.xrayMaterial);

    }
    //#region changeMaterial
    public changeMaterial = () => {
        const viewer = this._store.viewer!;
        viewer.scene.xrayMaterial.fill = true;
        viewer.scene.xrayMaterial.fillAlpha = 0.01;
        viewer.scene.xrayMaterial.fillColor = [0, 0, 0];
        viewer.scene.xrayMaterial.edgeAlpha = 0.02;
        viewer.scene.xrayMaterial.edgeColor = [0, 0, 0];

    }
    //#region removeEventListeners
    public removeEventListeners() {
        if (this._onMouseDown) {
            this._canvas?.removeEventListener('mousedown', this._onMouseDown);
            this._onMouseDown = null;
        }
        if (this._onMouseUp) {
            this._canvas?.removeEventListener('mouseup', this._onMouseUp);
            this._onMouseUp = null;
        }
        this.canvasContextMenu.hide();
        // this.resetMaterials();
    }

}