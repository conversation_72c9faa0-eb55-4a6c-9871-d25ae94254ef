<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Section Box Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #viewer {
            width: 800px;
            height: 600px;
            border: 1px solid #ccc;
            margin: 20px 0;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Section Box Dragging Test</h1>
    
    <div class="controls">
        <button onclick="toggleLimitDragging()">Toggle Limit Dragging</button>
        <button onclick="resetSectionBox()">Reset Section Box</button>
        <button onclick="showSectionBox()">Show Section Box</button>
        <button onclick="hideSectionBox()">Hide Section Box</button>
    </div>
    
    <div class="status">
        <div>Limit Dragging: <span id="limitStatus">true</span></div>
        <div>Instructions: 
            <ul>
                <li>Click "Toggle Limit Dragging" to switch between limited and unlimited dragging modes</li>
                <li>When limit dragging is OFF, you should be able to drag section box faces without affecting opposite faces</li>
                <li>When limit dragging is ON, dragging is constrained to the original AABB</li>
                <li>Try dragging different faces (left/right, top/bottom, front/back) to test the behavior</li>
            </ul>
        </div>
    </div>
    
    <div id="viewer"></div>

    <script type="module">
        import { Viewer, XKTLoaderPlugin, SectionPlanesPlugin } from './node_modules/@xeokit/xeokit-sdk/dist/xeokit-sdk.es.js';
        import { SectionBox } from './src/core/view/sections/sectionBox.js';
        
        // Initialize viewer
        const viewer = new Viewer({
            canvasId: "viewer",
            transparent: true
        });

        viewer.camera.eye = [-21.80, 4.01, 6.56];
        viewer.camera.look = [0, -5.75, 0];
        viewer.camera.up = [0.37, 0.91, -0.11];

        // Add some basic geometry for testing
        const scene = viewer.scene;
        
        // Create a simple test model
        scene.createMesh({
            id: "testBox1",
            primitive: "triangles",
            positions: [
                // Front face
                -1, -1,  1,
                 1, -1,  1,
                 1,  1,  1,
                -1,  1,  1,
                // Back face
                -1, -1, -1,
                -1,  1, -1,
                 1,  1, -1,
                 1, -1, -1,
                // Top face
                -1,  1, -1,
                -1,  1,  1,
                 1,  1,  1,
                 1,  1, -1,
                // Bottom face
                -1, -1, -1,
                 1, -1, -1,
                 1, -1,  1,
                -1, -1,  1,
                // Right face
                 1, -1, -1,
                 1,  1, -1,
                 1,  1,  1,
                 1, -1,  1,
                // Left face
                -1, -1, -1,
                -1, -1,  1,
                -1,  1,  1,
                -1,  1, -1
            ],
            indices: [
                0,  1,  2,      0,  2,  3,    // front
                4,  5,  6,      4,  6,  7,    // back
                8,  9,  10,     8,  10, 11,   // top
                12, 13, 14,     12, 14, 15,   // bottom
                16, 17, 18,     16, 18, 19,   // right
                20, 21, 22,     20, 22, 23    // left
            ]
        });

        // Initialize section box
        let sectionBox;
        try {
            sectionBox = new SectionBox();
            sectionBox.active = true;
            sectionBox.visible = true;
            
            // Set initial AABB
            sectionBox.aabb = [-2, -2, -2, 2, 2, 2];
            
            console.log("Section box initialized successfully");
        } catch (error) {
            console.error("Error initializing section box:", error);
        }

        // Global functions for buttons
        window.toggleLimitDragging = function() {
            if (sectionBox) {
                const currentLimit = sectionBox.getLimitDragging();
                sectionBox.setLimitDragging(!currentLimit);
                document.getElementById('limitStatus').textContent = !currentLimit;
                console.log("Limit dragging:", !currentLimit);
            }
        };

        window.resetSectionBox = function() {
            if (sectionBox) {
                sectionBox.reset();
                console.log("Section box reset");
            }
        };

        window.showSectionBox = function() {
            if (sectionBox) {
                sectionBox.visible = true;
                console.log("Section box shown");
            }
        };

        window.hideSectionBox = function() {
            if (sectionBox) {
                sectionBox.visible = false;
                console.log("Section box hidden");
            }
        };

        // Update status
        function updateStatus() {
            if (sectionBox) {
                document.getElementById('limitStatus').textContent = sectionBox.getLimitDragging();
            }
        }

        // Initial status update
        updateStatus();
        
        console.log("Test page loaded. Try toggling limit dragging and test section box behavior.");
    </script>
</body>
</html>
